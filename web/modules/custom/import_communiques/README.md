# Module Import Communiqués de Presse

Ce module permet d'importer des communiqués de presse depuis des fichiers JSON vers le type de contenu `communiques_de_presse` de Drupal.

## Fonctionnalités

- Import de communiqués de presse depuis des fichiers JSON
- Interface web d'administration pour l'upload et l'import
- Commandes Drush pour l'import en ligne de commande
- Mode aperçu pour prévisualiser les données avant import
- Validation des fichiers JSON
- Gestion automatique des secteurs (création de termes de taxonomie)
- Évite les doublons grâce à un ID externe

## Champs importés

Le module importe les champs suivants depuis le JSON :
- **Titre** : titre du communiqué de presse
- **Secteurs** : secteurs concernés (taxonomie `modes_de_transport` via le champ `field_secteur`)
- **Date** : date de publication du communiqué

## Structure du fichier JSON

Le module supporte deux formats de fichiers JSON :

### Format 1 : HTML structuré (recommandé)

```json
{
  "metadata": {
    "fileName": "Tableau communiqué de presse.docx",
    "fileSize": 57165,
    "lastModified": "2025-07-18T11:01:44.933Z",
    "conversionDate": "2025-07-18T13:42:28.573Z",
    "version": "1.0"
  },
  "content": {
    "html": "<table><tr><td>Date</td><td>Titre AR</td><td>Titre FR</td><td>ID</td><td>Secteurs AR</td><td>Secteurs FR</td></tr>...</table>"
  }
}
```

Le HTML doit contenir un tableau avec les colonnes :
- **Date** : Date du communiqué (dd/mm/yyyy)
- **Titre AR** : Titre en arabe
- **Titre FR** : Titre en français
- **Identifiant** : ID unique
- **Secteurs AR** : Secteurs en arabe (liste `<li>`)
- **Secteurs FR** : Secteurs en français (liste `<li>`)

### Format 2 : JSON simple (legacy)

```json
[
  {
    "id": 1,
    "titre": "Titre du communiqué",
    "secteurs": "Transport aérien; Transport maritime",
    "date": "19/04/2023"
  }
]
```

### Gestion des traductions

Quand un titre français et un titre arabe sont fournis, le module :
1. Crée le nœud principal en français
2. Ajoute automatiquement la traduction arabe
3. Associe les secteurs correspondants à chaque langue

## Installation

1. Placez le module dans `web/modules/custom/import_communiques/`
2. Activez le module : `drush en import_communiques`
3. Assurez-vous que le type de contenu `communiques_de_presse` existe
4. Assurez-vous que la taxonomie `secteur` existe

## Utilisation

### Interface Web

1. Allez sur `/admin/content/import-communiques`
2. Téléchargez votre fichier JSON
3. Cochez "Mode aperçu" pour prévisualiser sans importer
4. Cliquez sur "Importer"

### Commandes Drush

#### Importer un fichier JSON

```bash
# Import complet
vendor/bin/drush import-communiques:json /path/to/file.json

# Ou avec l'alias court
vendor/bin/drush icj /path/to/file.json

# Mode aperçu
vendor/bin/drush icj /path/to/file.json --preview
```

#### Valider un fichier JSON

```bash
# Validation
vendor/bin/drush import-communiques:validate /path/to/file.json

# Ou avec l'alias court
vendor/bin/drush icv /path/to/file.json
```

## Gestion des secteurs

Le module gère automatiquement les secteurs :
- Si un secteur existe déjà dans la taxonomie `modes_de_transport`, il est réutilisé
- Si un secteur n'existe pas, il est créé automatiquement
- Les secteurs multiples sont séparés par des points-virgules dans le JSON

## Gestion des doublons

Le module évite les doublons en utilisant un champ `field_external_id` qui stocke l'ID du JSON. Si un communiqué avec le même ID externe existe déjà, il sera mis à jour au lieu d'être créé.

## Prérequis

### Type de contenu

Le type de contenu `communiques_de_presse` doit exister avec les champs suivants :
- `field_external_id` : Champ texte pour l'ID externe
- `field_date` : Champ date
- `field_secteur` : Référence vers la taxonomie `modes_de_transport`

### Taxonomie

La taxonomie `modes_de_transport` doit exister pour stocker les secteurs.

## Logs et débogage

Le module utilise le système de logs de Drupal. Les erreurs sont enregistrées dans le canal `import_communiques`.

Pour voir les logs :
```bash
vendor/bin/drush watchdog:show --filter=import_communiques
```

## Exemple de fichier JSON complet

Voir le fichier `JSON/press.json` inclus dans le module pour un exemple complet.
