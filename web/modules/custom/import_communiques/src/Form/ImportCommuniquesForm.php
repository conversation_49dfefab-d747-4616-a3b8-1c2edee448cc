<?php

namespace Dr<PERSON>al\import_communiques\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\Core\Messenger\MessengerInterface;
use <PERSON><PERSON>al\import_communiques\Service\JsonImporter;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Formulaire d'importation des communiqués de presse.
 */
class ImportCommuniquesForm extends FormBase {

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The JSON importer service.
   *
   * @var \Drupal\import_communiques\Service\JsonImporter
   */
  protected $jsonImporter;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * Constructs a ImportCommuniquesForm object.
   */
  public function __construct(
    FileSystemInterface $file_system,
    JsonImporter $json_importer,
    MessengerInterface $messenger
  ) {
    $this->fileSystem = $file_system;
    $this->jsonImporter = $json_importer;
    $this->messenger = $messenger;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('file_system'),
      $container->get('import_communiques.json_importer'),
      $container->get('messenger')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'import_communiques_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['description'] = [
      '#markup' => '<p>' . $this->t('Utilisez ce formulaire pour importer des communiqués de presse depuis un fichier JSON.') . '</p>',
    ];

    $form['json_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichier JSON'),
      '#description' => $this->t('Sélectionnez le fichier JSON contenant les communiqués de presse à importer.'),
      '#upload_location' => 'public://imports/',
      '#required' => TRUE,
      '#upload_validators' => [
        'FileExtension' => ['extensions' => 'json'],
      ],
    ];

    $form['preview'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Mode aperçu'),
      '#description' => $this->t('Cochez cette case pour prévisualiser les données sans les importer.'),
      '#default_value' => FALSE,
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $file = $form_state->getValue('json_file');
    
    if (!empty($file)) {
      $file_entity = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
      $uri = $file_entity->getFileUri();
      $file_path = $this->fileSystem->realpath($uri);
      
      // Valider le JSON
      $json_content = file_get_contents($file_path);
      if ($json_content === FALSE) {
        $form_state->setErrorByName('json_file', $this->t('Impossible de lire le fichier JSON.'));
        return;
      }
      
      $data = json_decode($json_content, TRUE);
      if ($data === NULL) {
        $form_state->setErrorByName('json_file', $this->t('Le fichier JSON n\'est pas valide.'));
        return;
      }
      
      if (!is_array($data)) {
        $form_state->setErrorByName('json_file', $this->t('Le fichier JSON doit contenir un tableau d\'objets.'));
        return;
      }
      
      // Vérifier la structure selon le format
      if (isset($data['content']) && isset($data['content']['html'])) {
        // Nouveau format avec HTML - pas besoin de validation détaillée ici
        // La validation se fera lors du parsing
      }
      elseif (is_array($data) && isset($data[0])) {
        // Ancien format - valider la structure
        $required_fields = ['id', 'titre', 'secteurs', 'date'];
        foreach ($data as $index => $item) {
          if (!is_array($item)) {
            $form_state->setErrorByName('json_file', $this->t('L\'élément à l\'index @index n\'est pas un objet valide.', ['@index' => $index]));
            return;
          }

          foreach ($required_fields as $field) {
            if (!isset($item[$field])) {
              $form_state->setErrorByName('json_file', $this->t('Le champ "@field" est manquant dans l\'élément à l\'index @index.', [
                '@field' => $field,
                '@index' => $index,
              ]));
              return;
            }
          }
        }
      }
      else {
        $form_state->setErrorByName('json_file', $this->t('Format JSON non reconnu. Le fichier doit contenir soit un tableau d\'objets, soit un objet avec content.html.'));
        return;
      }
    }
    else {
      $form_state->setErrorByName('json_file', $this->t('Impossible d\'ouvrir le fichier JSON.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $file = $form_state->getValue('json_file');
    $preview_mode = $form_state->getValue('preview');

    if (!empty($file)) {
      $file_entity = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
      $uri = $file_entity->getFileUri();
      $file_path = $this->fileSystem->realpath($uri);

      if ($preview_mode) {
        // Mode aperçu
        $this->previewFile($file_path);
      }
      else {
        // Importation réelle
        $results = $this->jsonImporter->import($file_path);
        
        // Afficher les résultats
        if ($results['success']) {
          $this->messenger->addStatus($this->t('Importation terminée avec succès.'));
          $this->messenger->addStatus($this->t('Communiqués créés: @created', ['@created' => $results['created']]));
          $this->messenger->addStatus($this->t('Communiqués mis à jour: @updated', ['@updated' => $results['updated']]));
          $this->messenger->addStatus($this->t('Total traités: @processed', ['@processed' => $results['processed']]));
        }
        else {
          $this->messenger->addError($this->t('L\'importation a échoué.'));
        }
        
        // Afficher les erreurs
        if (!empty($results['errors'])) {
          foreach ($results['errors'] as $error) {
            $this->messenger->addError($error);
          }
        }
      }
    }
  }

  /**
   * Prévisualise le contenu du fichier JSON.
   */
  protected function previewFile($file_path) {
    $json_content = file_get_contents($file_path);
    $data = json_decode($json_content, TRUE);

    // Détecter le format et extraire les items
    if (isset($data['content']) && isset($data['content']['html'])) {
      // Nouveau format avec HTML
      $items = $this->parseHtmlContentForPreview($data['content']['html']);
      $this->messenger->addStatus($this->t('Format détecté: HTML structuré'));
      if (isset($data['metadata']['fileName'])) {
        $this->messenger->addStatus($this->t('Fichier source: @file', ['@file' => $data['metadata']['fileName']]));
      }
    }
    elseif (is_array($data) && isset($data[0]['id'])) {
      // Ancien format
      $items = $data;
      $this->messenger->addStatus($this->t('Format détecté: JSON simple'));
    }
    else {
      $this->messenger->addError($this->t('Format JSON non reconnu.'));
      return;
    }

    $this->messenger->addStatus($this->t('Nombre total d\'éléments: @count', ['@count' => count($items)]));

    // Afficher les 5 premiers éléments
    $preview_count = min(5, count($items));
    for ($i = 0; $i < $preview_count; $i++) {
      $item = $items[$i];
      $titre_display = $item['titre'] ?? 'N/A';
      if (!empty($item['titre_ar']) && $item['titre_ar'] !== $item['titre']) {
        $titre_display .= ' [AR: ' . substr($item['titre_ar'], 0, 30) . '...]';
      }

      $message = $this->t('Élément @index: ID=@id, Date=@date, Titre="@titre", Secteurs="@secteurs"', [
        '@index' => $i + 1,
        '@id' => $item['id'] ?? 'N/A',
        '@date' => $item['date'] ?? 'N/A',
        '@titre' => substr($titre_display, 0, 60) . (strlen($titre_display) > 60 ? '...' : ''),
        '@secteurs' => substr($item['secteurs'] ?? 'N/A', 0, 40) . (strlen($item['secteurs'] ?? '') > 40 ? '...' : ''),
      ]);
      $this->messenger->addStatus($message);
    }

    if (count($items) > 5) {
      $this->messenger->addStatus($this->t('... et @more autres éléments.', ['@more' => count($items) - 5]));
    }
  }

  /**
   * Parse le contenu HTML pour l'aperçu.
   */
  protected function parseHtmlContentForPreview($html_content) {
    // Utiliser le service d'import pour parser le HTML
    $importer = $this->jsonImporter;
    $reflection = new \ReflectionClass($importer);
    $method = $reflection->getMethod('parseHtmlContent');
    $method->setAccessible(TRUE);
    return $method->invoke($importer, $html_content);
  }

}
