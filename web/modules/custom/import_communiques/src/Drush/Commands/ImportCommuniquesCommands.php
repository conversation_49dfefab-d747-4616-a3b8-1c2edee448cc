<?php

namespace Drupal\import_communiques\Drush\Commands;

use Dr<PERSON>al\import_communiques\Service\JsonImporter;
use Drush\Attributes as CLI;
use Drush\Commands\DrushCommands;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Commandes Drush pour l'importation des communiqués de presse.
 */
final class ImportCommuniquesCommands extends DrushCommands {

  /**
   * The JSON importer service.
   *
   * @var \Drupal\import_communiques\Service\JsonImporter
   */
  protected $jsonImporter;

  /**
   * Constructs a ImportCommuniquesCommands object.
   */
  public function __construct(JsonImporter $json_importer) {
    parent::__construct();
    $this->jsonImporter = $json_importer;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('import_communiques.json_importer')
    );
  }

  /**
   * Importe les communiqués de presse depuis un fichier JSON.
   *
   * @param string $file_path
   *   Chemin vers le fichier JSON.
   * @param array $options
   *   Options de la commande.
   */
  #[CLI\Command(name: 'import-communiques:json', aliases: ['icj'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier JSON à importer')]
  #[CLI\Option(name: 'preview', description: 'Mode aperçu - affiche les données sans les importer')]
  #[CLI\Usage(name: 'import-communiques:json /path/to/file.json', description: 'Importe les communiqués depuis le fichier JSON spécifié')]
  #[CLI\Usage(name: 'import-communiques:json /path/to/file.json --preview', description: 'Prévisualise les données du fichier JSON sans les importer')]
  public function importJson($file_path, array $options = ['preview' => FALSE]) {
    $preview = $options['preview'];

    $this->output()->writeln("Début de l'importation depuis: <info>$file_path</info>");
    
    if ($preview) {
      $this->output()->writeln("<comment>MODE APERÇU - Aucune donnée ne sera importée</comment>");
    }

    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier n'existe pas: $file_path");
      return;
    }

    // Vérifier que c'est un fichier JSON
    if (pathinfo($file_path, PATHINFO_EXTENSION) !== 'json') {
      $this->logger()->error("Le fichier doit avoir l'extension .json");
      return;
    }

    if ($preview) {
      $this->previewFile($file_path);
    }
    else {
      // Effectuer l'importation
      $results = $this->jsonImporter->import($file_path);
      
      // Afficher les résultats
      $this->output()->writeln("Résultats de l'importation:");
      $this->output()->writeln("- Succès: " . ($results['success'] ? 'Oui' : 'Non'));
      $this->output()->writeln("- Créés: " . $results['created']);
      $this->output()->writeln("- Mis à jour: " . $results['updated']);
      $this->output()->writeln("- Traités: " . $results['processed']);
      $this->output()->writeln("- Erreurs: " . count($results['errors']));
      
      if (!empty($results['errors'])) {
        $this->output()->writeln("\nErreurs détaillées:");
        foreach ($results['errors'] as $error) {
          $this->logger()->error($error);
        }
      }
      
      if ($results['success']) {
        $this->logger()->success("Importation terminée avec succès!");
      }
      else {
        $this->logger()->error("L'importation a échoué.");
      }
    }
  }

  /**
   * Valide un fichier JSON sans l'importer.
   *
   * @param string $file_path
   *   Chemin vers le fichier JSON.
   */
  #[CLI\Command(name: 'import-communiques:validate', aliases: ['icv'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier JSON à valider')]
  #[CLI\Usage(name: 'import-communiques:validate /path/to/file.json', description: 'Valide la structure du fichier JSON')]
  public function validateJson($file_path) {
    $this->output()->writeln("Validation du fichier: <info>$file_path</info>");

    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier n'existe pas: $file_path");
      return;
    }

    // Lire et décoder le JSON
    $json_content = file_get_contents($file_path);
    if ($json_content === FALSE) {
      $this->logger()->error('Impossible de lire le fichier JSON.');
      return;
    }

    $data = json_decode($json_content, TRUE);
    if ($data === NULL) {
      $this->logger()->error('Le fichier JSON n\'est pas valide: ' . json_last_error_msg());
      return;
    }

    // Détecter le format et valider
    if (isset($data['content']) && isset($data['content']['html'])) {
      // Nouveau format avec HTML
      $this->output()->writeln("Format détecté: HTML structuré");
      try {
        $items = $this->parseHtmlContentForPreview($data['content']['html']);
        if (empty($items)) {
          $this->logger()->error('Aucun élément trouvé dans le contenu HTML.');
          return;
        }
      }
      catch (\Exception $e) {
        $this->logger()->error('Erreur lors du parsing HTML: ' . $e->getMessage());
        return;
      }
    }
    elseif (is_array($data) && isset($data[0]['id'])) {
      // Ancien format
      $this->output()->writeln("Format détecté: JSON simple");
      $items = $data;

      // Valider la structure de l'ancien format
      $required_fields = ['id', 'titre', 'secteurs', 'date'];
      $errors = [];

      foreach ($data as $index => $item) {
        if (!is_array($item)) {
          $errors[] = "L'élément à l'index $index n'est pas un objet valide.";
          continue;
        }

        foreach ($required_fields as $field) {
          if (!isset($item[$field])) {
            $errors[] = "Le champ '$field' est manquant dans l'élément à l'index $index.";
          }
        }
      }

      if (!empty($errors)) {
        $this->logger()->error("Le fichier JSON contient des erreurs:");
        foreach ($errors as $error) {
          $this->logger()->error("- $error");
        }
        return;
      }
    }
    else {
      $this->logger()->error('Format JSON non reconnu. Le fichier doit contenir soit un tableau d\'objets, soit un objet avec content.html.');
      return;
    }

    $errors = [];

    if (empty($errors)) {
      $this->logger()->success("Le fichier JSON est valide!");
      $this->output()->writeln("Nombre d'éléments: <info>" . count($data) . "</info>");
    }
    else {
      $this->logger()->error("Le fichier JSON contient des erreurs:");
      foreach ($errors as $error) {
        $this->logger()->error("- $error");
      }
    }
  }

  /**
   * Prévisualise le contenu d'un fichier JSON.
   */
  protected function previewFile($file_path) {
    $json_content = file_get_contents($file_path);
    $data = json_decode($json_content, TRUE);

    if ($data === NULL) {
      $this->logger()->error('Impossible de décoder le fichier JSON: ' . json_last_error_msg());
      return;
    }

    // Détecter le format et extraire les items
    if (isset($data['content']) && isset($data['content']['html'])) {
      // Nouveau format avec HTML
      $items = $this->parseHtmlContentForPreview($data['content']['html']);
      $this->output()->writeln("Format détecté: <info>HTML structuré</info>");
      if (isset($data['metadata']['fileName'])) {
        $this->output()->writeln("Fichier source: <info>" . $data['metadata']['fileName'] . "</info>");
      }
    }
    elseif (is_array($data) && isset($data[0]['id'])) {
      // Ancien format
      $items = $data;
      $this->output()->writeln("Format détecté: <info>JSON simple</info>");
    }
    else {
      $this->logger()->error('Format JSON non reconnu.');
      return;
    }

    $this->output()->writeln("Nombre total d'éléments: <info>" . count($items) . "</info>");

    // Afficher les 10 premiers éléments
    $preview_count = min(10, count($items));
    $this->output()->writeln("\nPremiers éléments:");

    for ($i = 0; $i < $preview_count; $i++) {
      $item = $items[$i];
      $titre_display = $item['titre'] ?? 'N/A';
      if (!empty($item['titre_ar']) && $item['titre_ar'] !== $item['titre']) {
        $titre_display .= ' [AR: ' . substr($item['titre_ar'], 0, 30) . '...]';
      }

      $this->output()->writeln(sprintf(
        "  %d. ID: %s | Date: %s | Titre: %s | Secteurs: %s",
        $i + 1,
        $item['id'] ?? 'N/A',
        $item['date'] ?? 'N/A',
        substr($titre_display, 0, 60) . (strlen($titre_display) > 60 ? '...' : ''),
        substr($item['secteurs'] ?? 'N/A', 0, 40) . (strlen($item['secteurs'] ?? '') > 40 ? '...' : '')
      ));
    }

    if (count($items) > 10) {
      $this->output()->writeln("  ... et " . (count($items) - 10) . " autres éléments.");
    }
  }

  /**
   * Parse le contenu HTML pour l'aperçu (version simplifiée).
   */
  protected function parseHtmlContentForPreview($html_content) {
    // Utiliser le même service que pour l'import
    $importer = \Drupal::service('import_communiques.json_importer');
    $reflection = new \ReflectionClass($importer);
    $method = $reflection->getMethod('parseHtmlContent');
    $method->setAccessible(TRUE);
    return $method->invoke($importer, $html_content);
  }

}
