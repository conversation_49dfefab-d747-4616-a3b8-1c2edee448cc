<?php

namespace Drupal\import_communiques\Service;

use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;

/**
 * Service pour importer des communiqués de presse depuis un fichier JSON.
 */
class JsonImporter {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;

  /**
   * Constructs a JsonImporter object.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    LoggerChannelFactoryInterface $logger_factory,
    LanguageManagerInterface $language_manager
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
    $this->languageManager = $language_manager;
  }

  /**
   * Importe les communiqués depuis un fichier JSON.
   *
   * @param string $file_path
   *   Le chemin vers le fichier JSON.
   *
   * @return array
   *   Résultats de l'importation.
   */
  public function import($file_path) {
    $results = [
      'success' => FALSE,
      'created' => 0,
      'updated' => 0,
      'errors' => [],
      'processed' => 0,
    ];

    try {
      // Lire le fichier JSON
      $json_content = file_get_contents($file_path);
      if ($json_content === FALSE) {
        $results['errors'][] = 'Impossible de lire le fichier JSON.';
        return $results;
      }

      // Décoder le JSON
      $data = json_decode($json_content, TRUE);
      if ($data === NULL) {
        $results['errors'][] = 'Fichier JSON invalide.';
        return $results;
      }

      // Détecter le format du JSON
      if (isset($data['content']) && isset($data['metadata'])) {
        // Nouveau format avec HTML
        $items = $this->parseHtmlContent($data['content']['html']);
      }
      elseif (is_array($data) && isset($data[0]['id'])) {
        // Ancien format avec tableau d'objets
        $items = $data;
      }
      else {
        $results['errors'][] = 'Format JSON non reconnu.';
        return $results;
      }

      // Traiter chaque communiqué
      foreach ($items as $item) {
        $results['processed']++;

        try {
          $this->processItem($item, $results);
        }
        catch (\Exception $e) {
          $results['errors'][] = 'Erreur lors du traitement de l\'item ID ' . ($item['id'] ?? 'inconnu') . ': ' . $e->getMessage();
        }
      }

      $results['success'] = TRUE;
    }
    catch (\Exception $e) {
      $results['errors'][] = 'Erreur générale: ' . $e->getMessage();
    }

    return $results;
  }

  /**
   * Traite un élément du JSON.
   */
  protected function processItem($item, &$results) {
    // Vérifier si le communiqué existe déjà
    $existing_node = $this->findExistingNode($item['id']);
    
    if ($existing_node) {
      // Mise à jour
      $node = $existing_node;
      $results['updated']++;
    }
    else {
      // Création
      $node = Node::create(['type' => 'communiques_de_presse']);
      $results['created']++;
    }

    // Mapper les champs
    $this->mapFields($node, $item);
    
    // Sauvegarder le nœud
    $node->save();
  }

  /**
   * Cherche un nœud existant par ID externe.
   */
  protected function findExistingNode($external_id) {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'communiques_de_presse')
      ->condition('field_external_id', $external_id)
      ->range(0, 1);

    $nids = $query->execute();
    
    if (!empty($nids)) {
      return Node::load(reset($nids));
    }
    
    return NULL;
  }

  /**
   * Mappe les champs du JSON vers les champs Drupal.
   */
  protected function mapFields($node, $item) {
    // Titre
    $titre = !empty($item['titre']) && $item['titre'] !== 'Sans titre' && $item['titre'] !== '???' 
      ? $item['titre'] 
      : 'Communiqué de presse #' . $item['id'];
    $node->setTitle($titre);

    // ID externe pour éviter les doublons
    if (isset($item['id'])) {
      $node->set('field_external_id', $item['id']);
    }

    // Date
    if (!empty($item['date']) && $item['date'] !== 'Non spécifiée') {
      $date = $this->parseDate($item['date']);
      if ($date) {
        $node->set('field_date', $date);
      }
    }

    // Secteurs
    if (!empty($item['secteurs']) && $item['secteurs'] !== 'Non spécifié') {
      $secteur_terms = $this->processSecteurs($item['secteurs']);
      if (!empty($secteur_terms)) {
        $node->set('field_secteur', $secteur_terms);
      }
    }

    // Langue par défaut
    $node->set('langcode', 'fr');
    $node->set('status', 1); // Publié
  }

  /**
   * Parse une date depuis le format du JSON.
   */
  protected function parseDate($date_string) {
    try {
      // Format attendu: dd/mm/yyyy
      if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $date_string, $matches)) {
        $day = $matches[1];
        $month = $matches[2];
        $year = $matches[3];
        return $year . '-' . $month . '-' . $day;
      }
    }
    catch (\Exception) {
      // Ignorer les erreurs de date
    }
    
    return NULL;
  }

  /**
   * Traite les secteurs et retourne les IDs des termes de taxonomie.
   */
  protected function processSecteurs($secteurs_string) {
    $secteur_ids = [];
    
    // Séparer les secteurs par point-virgule
    $secteurs = array_map('trim', explode(';', $secteurs_string));
    
    foreach ($secteurs as $secteur_name) {
      if (!empty($secteur_name)) {
        $term_id = $this->findOrCreateSecteurTerm($secteur_name);
        if ($term_id) {
          $secteur_ids[] = ['target_id' => $term_id];
        }
      }
    }
    
    return $secteur_ids;
  }

  /**
   * Trouve ou crée un terme de taxonomie pour un secteur.
   */
  protected function findOrCreateSecteurTerm($secteur_name) {
    // Chercher le terme existant
    $query = $this->entityTypeManager->getStorage('taxonomy_term')->getQuery()
      ->accessCheck(FALSE)
      ->condition('vid', 'modes_de_transport')
      ->condition('name', $secteur_name)
      ->range(0, 1);

    $tids = $query->execute();

    if (!empty($tids)) {
      return reset($tids);
    }

    // Créer un nouveau terme
    try {
      $term = Term::create([
        'vid' => 'modes_de_transport',
        'name' => $secteur_name,
        'langcode' => 'fr',
      ]);
      $term->save();
      return $term->id();
    }
    catch (\Exception $e) {
      $this->loggerFactory->get('import_communiques')->error('Erreur lors de la création du terme de secteur: @error', ['@error' => $e->getMessage()]);
      return NULL;
    }
  }

}
